@extends('layouts.app')

@section('title', 'Dashboard - Rental PS Admin')
@section('page-title', 'Dashboard')

@section('content')
<div class="row">
    <!-- Statistics Cards -->
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-gamepad fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_ps_units'] }}</h3>
                <p class="mb-0">Total PS Units</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['available_units'] }}</h3>
                <p class="mb-0">Available Units</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['rented_units'] }}</h3>
                <p class="mb-0">Rented Units</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_customers'] }}</h3>
                <p class="mb-0">Total Customers</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Revenue Cards -->
    <div class="col-md-6 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-3 text-dark"></i>
                <h4 class="mb-1 text-dark">Rp {{ number_format($stats['today_revenue'], 0, ',', '.') }}</h4>
                <p class="mb-0 text-dark">Today's Revenue</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-3 text-dark"></i>
                <h4 class="mb-1 text-dark">Rp {{ number_format($stats['month_revenue'], 0, ',', '.') }}</h4>
                <p class="mb-0 text-dark">This Month's Revenue</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Rental Status -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    Rental Status
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Active Rentals</span>
                    <span class="badge bg-success">{{ $stats['active_rentals'] }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Pending Rentals</span>
                    <span class="badge bg-warning">{{ $stats['pending_rentals'] }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Overdue Rentals</span>
                    <span class="badge bg-danger">{{ $stats['overdue_rentals'] }}</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Unit Status -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-gamepad me-2"></i>
                    Unit Status
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Available</span>
                    <span class="badge bg-success">{{ $stats['available_units'] }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Rented</span>
                    <span class="badge bg-primary">{{ $stats['rented_units'] }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Maintenance</span>
                    <span class="badge bg-warning">{{ $stats['maintenance_units'] }}</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.rentals.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>
                        New Rental
                    </a>
                    <a href="{{ route('admin.ps-units.create') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-gamepad me-1"></i>
                        Add PS Unit
                    </a>
                    <a href="{{ route('admin.rentals.index') }}" class="btn btn-info btn-sm">
                        <i class="fas fa-list me-1"></i>
                        View All Rentals
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Rentals -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    Recent Rentals
                </h5>
            </div>
            <div class="card-body">
                @if($recentRentals->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Rental Code</th>
                                    <th>Customer</th>
                                    <th>PS Unit</th>
                                    <th>Duration</th>
                                    <th>Total Price</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentRentals as $rental)
                                <tr>
                                    <td>
                                        <strong>{{ $rental->rental_code }}</strong>
                                    </td>
                                    <td>{{ $rental->customer->name }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $rental->psUnit->unit_code }}</span>
                                        {{ $rental->psUnit->ps_type }}
                                    </td>
                                    <td>{{ $rental->duration }} {{ $rental->rental_type === 'hourly' ? 'hours' : 'days' }}</td>
                                    <td>Rp {{ number_format($rental->total_price, 0, ',', '.') }}</td>
                                    <td>
                                        @php
                                            $statusClass = match($rental->status) {
                                                'pending' => 'warning',
                                                'active' => 'primary',
                                                'completed' => 'success',
                                                'cancelled' => 'secondary',
                                                'overdue' => 'danger',
                                                default => 'secondary'
                                            };
                                        @endphp
                                        <span class="badge bg-{{ $statusClass }}">{{ ucfirst($rental->status) }}</span>
                                    </td>
                                    <td>{{ $rental->created_at->format('d M Y') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent rentals found.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@if($alertUnits->count() > 0)
<!-- Alert Units -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Units Requiring Attention
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($alertUnits as $unit)
                    <div class="col-md-4 mb-3">
                        <div class="alert alert-warning mb-0">
                            <strong>{{ $unit->unit_code }}</strong> - {{ $unit->ps_type }}<br>
                            <small>Status: {{ ucfirst($unit->status) }}</small>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endif

@endsection
