<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\PsUnitController;
use App\Http\Controllers\Admin\RentalController;

// Redirect root ke login
Route::get('/', function () {
    return redirect('/login');
});

// Authentication routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Admin routes (protected by auth middleware)
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // PS Units management
    Route::resource('ps-units', PsUnitController::class);

    // Rentals management
    Route::resource('rentals', RentalController::class);

    // Additional rental routes
    Route::patch('rentals/{rental}/return', [RentalController::class, 'returnUnit'])->name('rentals.return');
    Route::patch('rentals/{rental}/extend', [RentalController::class, 'extend'])->name('rentals.extend');
});
