@extends('layouts.app')

@section('title', 'Add PS Unit - Rental PS Admin')
@section('page-title', 'Add New PS Unit')

@section('content')
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-gamepad me-2"></i>
                    Add New PS Unit
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.ps-units.store') }}">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="unit_code" class="form-label">Unit Code *</label>
                            <input type="text" 
                                   class="form-control @error('unit_code') is-invalid @enderror" 
                                   id="unit_code" 
                                   name="unit_code" 
                                   value="{{ old('unit_code') }}" 
                                   placeholder="e.g., PS001"
                                   required>
                            @error('unit_code')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="ps_type" class="form-label">PS Type *</label>
                            <select class="form-select @error('ps_type') is-invalid @enderror" 
                                    id="ps_type" 
                                    name="ps_type" 
                                    required>
                                <option value="">Select PS Type</option>
                                <option value="PlayStation 4" {{ old('ps_type') == 'PlayStation 4' ? 'selected' : '' }}>PlayStation 4</option>
                                <option value="PlayStation 4 Pro" {{ old('ps_type') == 'PlayStation 4 Pro' ? 'selected' : '' }}>PlayStation 4 Pro</option>
                                <option value="PlayStation 4 Slim" {{ old('ps_type') == 'PlayStation 4 Slim' ? 'selected' : '' }}>PlayStation 4 Slim</option>
                                <option value="PlayStation 5" {{ old('ps_type') == 'PlayStation 5' ? 'selected' : '' }}>PlayStation 5</option>
                                <option value="PlayStation 5 Digital" {{ old('ps_type') == 'PlayStation 5 Digital' ? 'selected' : '' }}>PlayStation 5 Digital</option>
                            </select>
                            @error('ps_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="brand" class="form-label">Brand *</label>
                            <input type="text" 
                                   class="form-control @error('brand') is-invalid @enderror" 
                                   id="brand" 
                                   name="brand" 
                                   value="{{ old('brand', 'Sony') }}" 
                                   required>
                            @error('brand')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="condition" class="form-label">Condition *</label>
                            <select class="form-select @error('condition') is-invalid @enderror" 
                                    id="condition" 
                                    name="condition" 
                                    required>
                                <option value="">Select Condition</option>
                                <option value="excellent" {{ old('condition') == 'excellent' ? 'selected' : '' }}>Excellent</option>
                                <option value="good" {{ old('condition', 'good') == 'good' ? 'selected' : '' }}>Good</option>
                                <option value="fair" {{ old('condition') == 'fair' ? 'selected' : '' }}>Fair</option>
                                <option value="poor" {{ old('condition') == 'poor' ? 'selected' : '' }}>Poor</option>
                            </select>
                            @error('condition')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="price_per_hour" class="form-label">Price per Hour (Rp) *</label>
                            <input type="number" 
                                   class="form-control @error('price_per_hour') is-invalid @enderror" 
                                   id="price_per_hour" 
                                   name="price_per_hour" 
                                   value="{{ old('price_per_hour') }}" 
                                   min="0"
                                   step="1000"
                                   placeholder="15000"
                                   required>
                            @error('price_per_hour')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="price_per_day" class="form-label">Price per Day (Rp) *</label>
                            <input type="number" 
                                   class="form-control @error('price_per_day') is-invalid @enderror" 
                                   id="price_per_day" 
                                   name="price_per_day" 
                                   value="{{ old('price_per_day') }}" 
                                   min="0"
                                   step="1000"
                                   placeholder="100000"
                                   required>
                            @error('price_per_day')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status *</label>
                            <select class="form-select @error('status') is-invalid @enderror" 
                                    id="status" 
                                    name="status" 
                                    required>
                                <option value="">Select Status</option>
                                <option value="available" {{ old('status', 'available') == 'available' ? 'selected' : '' }}>Available</option>
                                <option value="rented" {{ old('status') == 'rented' ? 'selected' : '' }}>Rented</option>
                                <option value="maintenance" {{ old('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                <option value="broken" {{ old('status') == 'broken' ? 'selected' : '' }}>Broken</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="purchase_date" class="form-label">Purchase Date</label>
                            <input type="date" 
                                   class="form-control @error('purchase_date') is-invalid @enderror" 
                                   id="purchase_date" 
                                   name="purchase_date" 
                                   value="{{ old('purchase_date') }}">
                            @error('purchase_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="accessories" class="form-label">Accessories</label>
                        <input type="text" 
                               class="form-control @error('accessories') is-invalid @enderror" 
                               id="accessories" 
                               name="accessories" 
                               value="{{ old('accessories') }}" 
                               placeholder="2 Controller, HDMI Cable, Power Cable, Game Disc (separate with comma)">
                        <div class="form-text">Separate multiple accessories with comma</div>
                        @error('accessories')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="3"
                                  placeholder="Additional description about this PS unit">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.ps-units.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save PS Unit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Auto calculate daily price based on hourly price
    document.getElementById('price_per_hour').addEventListener('input', function() {
        const hourlyPrice = parseFloat(this.value) || 0;
        const dailyPrice = hourlyPrice * 8; // 8 hours = 1 day
        document.getElementById('price_per_day').value = dailyPrice;
    });
</script>
@endsection
