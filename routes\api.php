<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PsUnitController;
use App\Http\Controllers\Api\RentalController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Authentication routes
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
    
    // Public PS Units (for browsing without login)
    Route::get('/ps-units', [PsUnitController::class, 'index']);
    Route::get('/ps-units/available', [PsUnitController::class, 'available']);
    Route::get('/ps-units/{id}', [PsUnitController::class, 'show']);
});

// Protected routes (authentication required)
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // User profile
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);
    Route::post('/change-password', [AuthController::class, 'changePassword']);
    
    // Rentals
    Route::get('/rentals', [RentalController::class, 'index']); // User's rental history
    Route::post('/rentals', [RentalController::class, 'store']); // Create new rental
    Route::get('/rentals/{id}', [RentalController::class, 'show']); // Rental details
    Route::patch('/rentals/{id}/cancel', [RentalController::class, 'cancel']); // Cancel rental
    
    // PS Units (authenticated user can see more details - same as public for now)
});
