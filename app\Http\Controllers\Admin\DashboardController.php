<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PsUnit;
use App\Models\Rental;
use App\Models\Customer;

class DashboardController extends Controller
{
    // Middleware handled in routes

    public function index()
    {
        // Statistik untuk dashboard
        $stats = [
            'total_ps_units' => PsUnit::count(),
            'available_units' => PsUnit::where('status', 'available')->count(),
            'rented_units' => PsUnit::where('status', 'rented')->count(),
            'maintenance_units' => PsUnit::where('status', 'maintenance')->count(),
            'total_customers' => Customer::count(),
            'active_rentals' => Rental::where('status', 'active')->count(),
            'pending_rentals' => Rental::where('status', 'pending')->count(),
            'overdue_rentals' => Rental::where('status', 'overdue')->count(),
            'today_revenue' => Rental::whereDate('created_at', today())
                                   ->where('payment_status', 'paid')
                                   ->sum('final_amount'),
            'month_revenue' => Rental::whereMonth('created_at', now()->month)
                                   ->whereYear('created_at', now()->year)
                                   ->where('payment_status', 'paid')
                                   ->sum('final_amount'),
        ];

        // Recent rentals
        $recentRentals = Rental::with(['customer', 'psUnit'])
                              ->orderBy('created_at', 'desc')
                              ->limit(10)
                              ->get();

        // PS Units yang perlu perhatian
        $alertUnits = PsUnit::whereIn('status', ['maintenance', 'broken'])->get();

        return view('admin.dashboard', compact('stats', 'recentRentals', 'alertUnits'));
    }
}
