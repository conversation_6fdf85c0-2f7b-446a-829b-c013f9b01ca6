<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Builder;

class Customer extends Authenticatable
{
    use HasApiTokens, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'birth_date',
        'gender',
        'id_card_number',
        'status',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'birth_date' => 'date',
        'password' => 'hashed',
    ];

    // Relationship dengan rentals
    public function rentals(): HasMany
    {
        return $this->hasMany(Rental::class);
    }

    // Scope untuk customer aktif
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    // Check apakah customer aktif
    public function isActive(): bool
    {
        return $this->getAttribute('status') === 'active';
    }

    // Get rental history
    public function getRentalHistory()
    {
        return $this->rentals()->with('psUnit')->orderBy('created_at', 'desc')->get();
    }
}
