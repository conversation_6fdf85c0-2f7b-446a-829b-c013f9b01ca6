@extends('layouts.app')

@section('title', 'PS Units - Rental PS Admin')
@section('page-title', 'PS Units Management')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <h4>
            <i class="fas fa-gamepad me-2"></i>
            PS Units
        </h4>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ route('admin.ps-units.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Add New PS Unit
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        @if($psUnits->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Unit Code</th>
                            <th>PS Type</th>
                            <th>Brand</th>
                            <th>Price/Hour</th>
                            <th>Price/Day</th>
                            <th>Status</th>
                            <th>Condition</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($psUnits as $unit)
                        <tr>
                            <td>
                                <strong>{{ $unit->unit_code }}</strong>
                            </td>
                            <td>{{ $unit->ps_type }}</td>
                            <td>{{ $unit->brand }}</td>
                            <td>Rp {{ number_format($unit->price_per_hour, 0, ',', '.') }}</td>
                            <td>Rp {{ number_format($unit->price_per_day, 0, ',', '.') }}</td>
                            <td>
                                @php
                                    $statusClass = match($unit->status) {
                                        'available' => 'success',
                                        'rented' => 'primary',
                                        'maintenance' => 'warning',
                                        'broken' => 'danger',
                                        default => 'secondary'
                                    };
                                @endphp
                                <span class="badge bg-{{ $statusClass }}">{{ ucfirst($unit->status) }}</span>
                            </td>
                            <td>
                                @php
                                    $conditionClass = match($unit->condition) {
                                        'excellent' => 'success',
                                        'good' => 'info',
                                        'fair' => 'warning',
                                        'poor' => 'danger',
                                        default => 'secondary'
                                    };
                                @endphp
                                <span class="badge bg-{{ $conditionClass }}">{{ ucfirst($unit->condition) }}</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.ps-units.show', $unit) }}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.ps-units.edit', $unit) }}" 
                                       class="btn btn-sm btn-outline-warning" 
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" 
                                          action="{{ route('admin.ps-units.destroy', $unit) }}" 
                                          class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this PS Unit?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="btn btn-sm btn-outline-danger" 
                                                title="Delete"
                                                {{ $unit->status === 'rented' ? 'disabled' : '' }}>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $psUnits->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-gamepad fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">No PS Units Found</h5>
                <p class="text-muted">Start by adding your first PS unit to the inventory.</p>
                <a href="{{ route('admin.ps-units.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Add First PS Unit
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4>{{ $psUnits->where('status', 'available')->count() }}</h4>
                <small>Available</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <h4>{{ $psUnits->where('status', 'rented')->count() }}</h4>
                <small>Rented</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
            <div class="card-body">
                <i class="fas fa-tools fa-2x mb-2"></i>
                <h4>{{ $psUnits->where('status', 'maintenance')->count() }}</h4>
                <small>Maintenance</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4>{{ $psUnits->where('status', 'broken')->count() }}</h4>
                <small>Broken</small>
            </div>
        </div>
    </div>
</div>
@endsection
