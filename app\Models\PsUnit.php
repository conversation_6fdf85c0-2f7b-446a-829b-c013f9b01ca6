<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class PsUnit extends Model
{
    protected $fillable = [
        'unit_code',
        'ps_type',
        'brand',
        'description',
        'price_per_hour',
        'price_per_day',
        'status',
        'accessories',
        'condition',
        'purchase_date',
    ];

    protected $casts = [
        'accessories' => 'array',
        'price_per_hour' => 'decimal:2',
        'price_per_day' => 'decimal:2',
        'purchase_date' => 'date',
    ];

    // Relationship dengan rentals
    public function rentals(): HasMany
    {
        return $this->hasMany(Rental::class);
    }

    // Scope untuk unit yang tersedia
    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where('status', 'available');
    }

    // Scope untuk unit yang sedang disewa
    public function scopeRented(Builder $query): Builder
    {
        return $query->where('status', 'rented');
    }

    // Check apakah unit sedang disewa
    public function isRented(): bool
    {
        return $this->getAttribute('status') === 'rented';
    }

    // Check apakah unit tersedia
    public function isAvailable(): bool
    {
        return $this->getAttribute('status') === 'available';
    }
}
