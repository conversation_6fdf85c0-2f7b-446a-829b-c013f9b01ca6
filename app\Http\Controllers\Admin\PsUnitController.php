<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PsUnit;

class PsUnitController extends Controller
{
    // Middleware handled in routes

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $psUnits = PsUnit::orderBy('created_at', 'desc')->paginate(10);
        return view('admin.ps-units.index', compact('psUnits'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.ps-units.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'unit_code' => 'required|unique:ps_units',
            'ps_type' => 'required',
            'brand' => 'required',
            'price_per_hour' => 'required|numeric|min:0',
            'price_per_day' => 'required|numeric|min:0',
            'status' => 'required|in:available,rented,maintenance,broken',
            'condition' => 'required',
            'purchase_date' => 'nullable|date',
        ]);

        $data = $request->all();

        // Handle accessories as array
        if ($request->has('accessories')) {
            $data['accessories'] = array_filter(explode(',', $request->get('accessories')));
        }

        PsUnit::create($data);

        return redirect()->route('admin.ps-units.index')
                        ->with('success', 'PS Unit berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(PsUnit $psUnit)
    {
        return view('admin.ps-units.show', compact('psUnit'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PsUnit $psUnit)
    {
        return view('admin.ps-units.edit', compact('psUnit'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PsUnit $psUnit)
    {
        $request->validate([
            'unit_code' => 'required|unique:ps_units,unit_code,' . $psUnit->getKey(),
            'ps_type' => 'required',
            'brand' => 'required',
            'price_per_hour' => 'required|numeric|min:0',
            'price_per_day' => 'required|numeric|min:0',
            'status' => 'required|in:available,rented,maintenance,broken',
            'condition' => 'required',
            'purchase_date' => 'nullable|date',
        ]);

        $data = $request->all();

        // Handle accessories as array
        if ($request->has('accessories')) {
            $data['accessories'] = array_filter(explode(',', $request->get('accessories')));
        }

        $psUnit->update($data);

        return redirect()->route('admin.ps-units.index')
                        ->with('success', 'PS Unit berhasil diupdate.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PsUnit $psUnit)
    {
        // Check if unit is currently rented
        if ($psUnit->getAttribute('status') === 'rented') {
            return redirect()->route('admin.ps-units.index')
                            ->with('error', 'Tidak dapat menghapus PS Unit yang sedang disewa.');
        }

        $psUnit->delete();

        return redirect()->route('admin.ps-units.index')
                        ->with('success', 'PS Unit berhasil dihapus.');
    }
}
